"""
Implementação da busca automatizada no Google Maps para o PROSPECTO.
Sistema de captura de leads e automação de mensagens.
"""
import threading
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from PyQt6.QtCore import QThread, pyqtSignal

from google_maps_integration import move_map, BusinessList

class AutomatedSearchThread(QThread):
    """Thread para executar buscas automatizadas no Google Maps."""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(str)
    continue_query = pyqtSignal(str, bool)
    query_completed = pyqtSignal(int)  # Sinal para indicar que uma consulta foi concluída

    def __init__(self, search_queries, save_dir, file_format, headless_mode=True):
        super().__init__()
        self.search_queries = search_queries
        self.save_dir = save_dir
        self.file_format = file_format
        self.headless_mode = headless_mode
        self.current_query_index = 0
        self.running = False
        self.paused = False
        self.continue_response = None

    def run(self):
        """Executa as buscas automatizadas."""
        self.running = True
        navegador = None

        try:
            # Configurar o Chrome
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--enable-unsafe-swiftshader")
            chrome_options.add_argument("--disable-software-rasterizer")

            # Adicionar modo headless se solicitado
            if self.headless_mode:
                chrome_options.add_argument("--headless")
                chrome_options.add_argument("--window-size=1920,1080")

            # Configurar user-agent para evitar detecção de headless
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            navegador = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

            self.status_updated.emit("[INFO] Abrindo Google Maps...")
            navegador.get("https://www.google.com.br/maps")
            time.sleep(3)

            # Executar cada consulta de busca
            for i, query in enumerate(self.search_queries):
                if not self.running:
                    break

                self.current_query_index = i

                # Aguardar se estiver pausado
                while self.paused and self.running:
                    time.sleep(1)

                # Atualizar progresso
                progress = int((i / len(self.search_queries)) * 100)
                self.progress_updated.emit(progress)
                self.status_updated.emit(f"[INFO] Executando busca {i+1}/{len(self.search_queries)}: {query}")

                # Limpar busca anterior
                search_box = navegador.find_element(By.XPATH, '//*[@id="searchboxinput"]')
                search_box.clear()
                time.sleep(1)

                # Inserir nova busca
                search_box.send_keys(query)
                time.sleep(1)
                navegador.find_element(By.XPATH, '//*[@id="searchbox-searchbutton"]').click()
                time.sleep(10)

                # Extrair dados de negócios
                business_list = self._extract_business_data(navegador, query)

                # Salvar resultados
                if business_list.business_list:
                    self._save_results(business_list, query)
                    self.query_completed.emit(i)  # Emitir sinal de consulta concluída

                # Aguardar entre buscas
                time.sleep(5)

            self.status_updated.emit("[INFO] Todas as buscas foram concluídas.")
            self.progress_updated.emit(100)
            self.finished_signal.emit("Todas as buscas automatizadas foram concluídas com sucesso.")

        except Exception as e:
            error_msg = f"[ERRO] {str(e)}"
            self.status_updated.emit(error_msg)
            self.finished_signal.emit(error_msg)
        finally:
            try:
                if navegador:
                    navegador.quit()
            except:
                pass
            self.running = False

    def _extract_business_data(self, navegador, query, max_results=50):
        """Extrai dados de negócios do Google Maps."""
        business_list = BusinessList()
        i = 0
        move_count = 0
        max_moves = 4

        while i < max_results and move_count < max_moves and self.running:
            # Aguardar se estiver pausado
            while self.paused and self.running:
                time.sleep(1)

            previously_counted = 0
            stuck_count = 0

            while True:
                list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
                if not list_elem:
                    self.status_updated.emit("[AVISO] Nenhum elemento encontrado na página.")
                    return business_list

                action = ActionChains(navegador)
                try:
                    action.move_to_element(list_elem[-1]).perform()
                except Exception:
                    list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
                    action.move_to_element(list_elem[-1]).perform()
                time.sleep(5)

                # Rolar para baixo para carregar mais resultados
                from selenium.webdriver.common.actions.wheel_input import ScrollOrigin
                scroll_origin = ScrollOrigin.from_element(list_elem[-1])
                action.scroll_from_origin(scroll_origin, 0, 1200).perform()
                time.sleep(20)
                action.scroll_from_origin(scroll_origin, 0, 250).perform()

                current_count = len(list_elem)
                if current_count == previously_counted:
                    stuck_count += 1
                    if stuck_count >= 3:  # Se ficar preso 3 vezes, consideramos que chegamos ao limite
                        break
                else:
                    stuck_count = 0
                    previously_counted = current_count

            # Processa os elementos encontrados
            list_elem = navegador.find_elements(By.CLASS_NAME, 'hfpxzc')
            for element in list_elem[i:]:
                if i >= max_results or not self.running:
                    break

                try:
                    time.sleep(2)
                    navegador.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    time.sleep(2)
                    try:
                        element.click()
                    except Exception as click_err:
                        self.status_updated.emit(f"[ERRO] Falha ao clicar no elemento {i}: {str(click_err)}")
                        i += 1
                        continue
                    time.sleep(6)

                    # XPaths para extração dos dados
                    name_xpath = '//*[@id="QA0Szd"]/div/div/div[1]/div[3]/div/div[1]/div/div/div[2]/div[2]/div/div[1]/div[1]/h1'
                    address_xpath = '//button[@data-item-id="address"]//div[contains(@class, "fontBodyMedium")]'
                    website_xpath = '//a[@data-item-id="authority"]//div[contains(@class, "fontBodyMedium")]'
                    phone_number_xpath = '//button[contains(@data-item-id, "phone:tel:")]//div[contains(@class, "fontBodyMedium")]'

                    # Extrai os dados
                    from google_maps_integration import Business
                    business = Business()

                    try:
                        business.nome = navegador.find_element(By.XPATH, name_xpath).text
                    except:
                        business.nome = f"Nome não disponível {i}"

                    try:
                        business.endereco = navegador.find_element(By.XPATH, address_xpath).text
                    except:
                        business.endereco = "Endereço não disponível"

                    try:
                        business.site = navegador.find_element(By.XPATH, website_xpath).text
                    except:
                        business.site = "Site não disponível"

                    try:
                        business.telefone = navegador.find_element(By.XPATH, phone_number_xpath).text
                    except:
                        business.telefone = "Telefone não disponível"

                    # Adiciona o negócio à lista
                    business_list.business_list.append(business)
                    time.sleep(3)
                    i += 1

                    # Atualizar progresso
                    self.status_updated.emit(f"[PROGRESSO] Processando registro {i} de {max_results} para '{query}'")

                except Exception as e:
                    self.status_updated.emit(f"[ERRO] {str(e)}")
                    i += 1

            # Se ainda não atingimos o total desejado, move o mapa
            if i < max_results and self.running:
                directions = ['right', 'down', 'left', 'up']
                move_map(navegador, directions[move_count % len(directions)])
                move_count += 1
                time.sleep(5)

        return business_list

    def _save_results(self, business_list, query):
        """Salva os resultados em arquivo."""
        try:
            # Criar um nome de arquivo seguro
            safe_query = query.replace(" ", "_").replace("/", "_").replace("\\", "_")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"automated_search_{safe_query}_{timestamp}"

            # Salvar no formato especificado
            result = None
            if self.file_format.lower() == "excel":
                result = business_list.save_to_excel(filename, self.save_dir)
            elif self.file_format.lower() == "csv":
                result = business_list.save_to_csv(filename, self.save_dir)
            else:
                result = "Formato de arquivo inválido."

            self.status_updated.emit(result)

        except Exception as e:
            self.status_updated.emit(f"[ERRO] Falha ao salvar resultados: {str(e)}")

    def pause(self):
        """Pausa a busca automatizada."""
        self.paused = True
        self.status_updated.emit("[INFO] Busca automatizada pausada.")

    def resume(self):
        """Retoma a busca automatizada."""
        self.paused = False
        self.status_updated.emit("[INFO] Busca automatizada retomada.")

    def stop(self):
        """Para a busca automatizada."""
        self.running = False
        self.status_updated.emit("[INFO] Busca automatizada interrompida.")
