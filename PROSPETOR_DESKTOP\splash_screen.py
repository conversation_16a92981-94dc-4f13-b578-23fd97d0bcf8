import sys
from PyQt6.QtWidgets import <PERSON>A<PERSON><PERSON>, QSplashScreen, QLabel, QVBoxLayout, QWidget
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QFont, QPainter, QColor, QBrush, QPen
from resource_path import resource_path

class SplashScreen(QSplashScreen):
    def __init__(self):
        # Criar um pixmap personalizado com fundo branco e bordas arredondadas
        base_pixmap = QPixmap(400, 400)
        base_pixmap.fill(Qt.GlobalColor.transparent)

        # Carregar a logo
        logo_pixmap = QPixmap(resource_path('logo.png'))
        scaled_logo = logo_pixmap.scaled(200, 200, Qt.AspectRatioMode.KeepAspectRatio,
                                        Qt.TransformationMode.SmoothTransformation)

        # Desenhar o fundo e a logo
        painter = QPainter(base_pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Desenhar fundo branco com bordas arredondadas
        painter.setBrush(QBrush(QColor("#ffffff")))
        painter.setPen(QPen(QColor("#dddddd"), 2))
        painter.drawRoundedRect(0, 0, 400, 400, 20, 20)

        # Desenhar a logo centralizada
        logo_x = (base_pixmap.width() - scaled_logo.width()) // 2
        logo_y = 70
        painter.drawPixmap(logo_x, logo_y, scaled_logo)

        # Desenhar o subtítulo
        painter.setFont(QFont('Segoe UI', 18, QFont.Weight.Bold))
        painter.setPen(QPen(QColor("#333333")))
        painter.drawText(0, logo_y + scaled_logo.height() + 40,
                        base_pixmap.width(), 40,
                        Qt.AlignmentFlag.AlignCenter, "Lead Finder")

        # Desenhar a versão
        painter.setFont(QFont('Segoe UI', 10))
        painter.setPen(QPen(QColor("#999999")))
        painter.drawText(0, logo_y + scaled_logo.height() + 100,
                        base_pixmap.width(), 20,
                        Qt.AlignmentFlag.AlignCenter, "v1.0.0")

        # Desenhar mensagem de carregamento
        painter.setFont(QFont('Segoe UI', 9))
        painter.setPen(QPen(QColor("#999999")))
        painter.drawText(0, base_pixmap.height() - 40,
                        base_pixmap.width(), 20,
                        Qt.AlignmentFlag.AlignCenter, "Carregando...")

        painter.end()

        super().__init__(base_pixmap)

        # Configurar janela
        self.setWindowFlag(Qt.WindowType.WindowStaysOnTopHint)
        self.setWindowFlag(Qt.WindowType.FramelessWindowHint)

    def mousePressEvent(self, event):
        # Permitir fechar a tela de splash com um clique
        self.close()

def show_splash(app, duration=3000):
    """Show splash screen for the specified duration in milliseconds"""
    splash = SplashScreen()
    splash.show()

    # Close the splash screen after duration
    QTimer.singleShot(duration, splash.close)

    return splash

if __name__ == "__main__":
    # Test the splash screen
    app = QApplication(sys.argv)
    splash = show_splash(app)
    sys.exit(app.exec())
