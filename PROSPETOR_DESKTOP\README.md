# TeemoFlow

Ferramenta avançada para captura automatizada de leads do Google Maps, com funcionalidades de busca, automação e envio de mensagens.

> **🚀 Migração para Web em Andamento**
> Este projeto está sendo migrado de uma aplicação desktop (PyQt6) para uma aplicação web moderna (React + FastAPI).

## Funcionalidades

### Busca por CEP
- Captura de leads do Google Maps por CEP e palavra-chave
- Extração de informações como nome, telefone, endereço e site
- Exportação para Excel

### Google Maps
- Busca direta no Google Maps por termo e localização
- Configuração de quantidade de resultados
- Exportação em Excel ou CSV

### Busca Automatizada
- Configuração de múltiplas buscas para execução sequencial
- Controles de pausa e retomada
- Exportação automática dos resultados

### Mensagens
- Importação de contatos de arquivos Excel/CSV
- Uso de leads capturados como contatos
- Geração de variações de mensagens com IA
- Envio de mensagens via WhatsApp Web
- Controles de pausa e retomada

## Requisitos

- Python 3.8 ou superior
- PyQt6
- Selenium
- pandas
- openpyxl
- Bibliotecas listadas em `requirements.txt`

## Instalação das dependências

```
pip install -r requirements.txt
```

## 🚀 Executando o TeemoFlow (Web)

### Pré-requisitos
- Python 3.11+
- Node.js 18+
- Docker (opcional)
- Google Chrome

### Instalação e Execução

#### Método 1: Docker (Recomendado)
```bash
# Clonar o repositório
git clone <repository-url>
cd teemoflow

# Iniciar com Docker Compose
docker-compose up -d

# Acessar a aplicação
# Frontend: http://localhost:3000
# Backend API: http://localhost:8000
# Documentação API: http://localhost:8000/docs
```

#### Método 2: Desenvolvimento Local

**Backend:**
```bash
cd backend
pip install -r requirements.txt
cp .env.example .env
uvicorn main:app --reload
```

**Frontend:**
```bash
cd frontend
npm install
npm run dev
```

### Interface Desktop (Legado)
```bash
# Interface original (PyQt6)
python ui.py

# PROSPECTO - Interface avançada (PyQt6)
python run_new_ui.py
```

## Compilando para executável Windows (.exe)

### Método 1: Usando o script de compilação segura

Este método é recomendado para criar um executável que minimize falsos positivos em antivírus:

1. Execute o script de compilação segura:

```
python build_safe_exe.py
```

2. O script irá:
   - Verificar e instalar dependências necessárias
   - Limpar builds anteriores
   - Compilar o executável com configurações otimizadas
   - Verificar o build resultante

3. O executável será criado na pasta `dist/`

### Método 2: Usando auto-py-to-exe

Para uma interface gráfica de compilação:

1. Execute o script auxiliar:

```
python compile_gui.py
```

2. Siga as instruções na interface do auto-py-to-exe:
   - Em 'Script Location' selecione o arquivo ui.py
   - Selecione 'One File' e 'Window Based (hide the console)'
   - Em 'Icon' selecione o arquivo prospecto.png
   - Em 'Additional Files' adicione prospecto.png
   - Em 'Version File' selecione file_version_info.txt
   - Clique em 'CONVERT .PY TO .EXE'

## Notas importantes

- O Chrome Driver é baixado automaticamente pela aplicação
- É necessário ter o Google Chrome instalado no computador
- O arquivo `prospecto.png` deve estar presente no mesmo diretório que o executável

## 🌟 Funcionalidades da Versão Web

### Interface Moderna
- **Dashboard intuitivo** com estatísticas em tempo real
- **Design responsivo** para desktop e mobile
- **Navegação por abas** para melhor organização
- **Tema moderno** com Material-UI

### Funcionalidades Principais
- **Busca por CEP** - Captura leads por CEP e palavra-chave
- **Google Maps** - Busca direta por termo e localização
- **Busca Automatizada** - Múltiplas consultas sequenciais
- **Sistema de Mensagens** - Envio via WhatsApp Web com IA
- **Gerenciamento de Jobs** - Controle total das operações
- **Gerenciamento de Arquivos** - Upload/download facilitado

### Tecnologias
- **Backend**: FastAPI + Python
- **Frontend**: React + Material-UI
- **WebSockets**: Atualizações em tempo real
- **Selenium**: Automação web
- **Docker**: Containerização

### Funcionalidades Legado (Desktop)
O TeemoFlow mantém compatibilidade com as versões desktop originais:
- Interface PyQt6 moderna com abas
- Sistema de log detalhado
- Modo headless configurável
- Controles avançados (pausa, retomada, cancelamento)
- Exportação em múltiplos formatos (Excel/CSV)

## Troubleshooting

Se ocorrer algum erro durante a execução:

1. Verifique se todas as dependências estão instaladas
2. Certifique-se de que o Google Chrome está atualizado
3. Verifique os logs de erro na aplicação

## Reduzindo Falsos Positivos em Antivírus

Se o executável for detectado como falso positivo por antivírus:

1. **Adicione exceções no antivírus**:
   - Adicione o executável à lista de exceções do seu antivírus
   - Adicione a pasta onde o executável está localizado às exceções

2. **Use o script de compilação segura**:
   - O script `build_safe_exe.py` usa configurações otimizadas para reduzir falsos positivos

3. **Assine digitalmente o executável**:
   - A assinatura digital pode reduzir significativamente os falsos positivos
   - Requer um certificado de assinatura de código (pago)

4. **Reporte o falso positivo**:
   - Contate o suporte do antivírus para reportar o falso positivo

## Licença

Copyright © 2023-2024 Lead Capture Tools. Todos os direitos reservados.