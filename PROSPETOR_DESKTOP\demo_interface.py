#!/usr/bin/env python3
"""
Demonstração da interface corrigida - Estados visíveis e tela cheia
"""

import sys
import os

# Adicionar o diretório atual ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
        QLabel, QComboBox, QLineEdit, QPushButton, QFrame, QCompleter
    )
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    
    print("✓ PyQt6 importado com sucesso")
    
    # Importar constantes
    from ui import ESTADOS_BRASILEIROS, obter_bairros_estado
    print("✓ Constantes importadas com sucesso")
    
    class DemoInterface(QMainWindow):
        def __init__(self):
            super().__init__()
            self.initUI()
        
        def initUI(self):
            # Configurar janela principal
            self.setWindowTitle("Demo - Interface Corrigida: Estados Visíveis + Tela Cheia")
            self.showMaximized()  # TELA CHEIA
            
            # Widget central
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # Layout principal
            main_layout = QVBoxLayout(central_widget)
            main_layout.setContentsMargins(50, 50, 50, 50)
            main_layout.setSpacing(30)
            
            # Título
            title = QLabel("🎯 INTERFACE CORRIGIDA")
            title.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title.setFont(QFont('Segoe UI', 24, QFont.Weight.Bold))
            title.setStyleSheet("color: #2c3e50; margin-bottom: 20px;")
            main_layout.addWidget(title)
            
            # Subtítulo
            subtitle = QLabel("✅ Estados agora estão visíveis (texto escuro)\n✅ Aplicação abre em tela cheia")
            subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
            subtitle.setFont(QFont('Segoe UI', 14))
            subtitle.setStyleSheet("color: #27ae60; margin-bottom: 30px;")
            main_layout.addWidget(subtitle)
            
            # Frame de demonstração
            demo_frame = QFrame()
            demo_frame.setObjectName("demoFrame")
            demo_layout = QVBoxLayout(demo_frame)
            demo_layout.setContentsMargins(40, 40, 40, 40)
            demo_layout.setSpacing(20)
            
            # Estado
            estado_layout = QHBoxLayout()
            label_estado = QLabel("Estado:")
            label_estado.setMinimumWidth(100)
            self.combo_estado = QComboBox()
            self.combo_estado.addItems(ESTADOS_BRASILEIROS)
            self.combo_estado.setCurrentText("São Paulo (SP)")
            self.combo_estado.currentTextChanged.connect(self.atualizar_bairros)
            estado_layout.addWidget(label_estado)
            estado_layout.addWidget(self.combo_estado)
            demo_layout.addLayout(estado_layout)
            
            # Bairro
            bairro_layout = QHBoxLayout()
            label_bairro = QLabel("Bairro:")
            label_bairro.setMinimumWidth(100)
            self.entrada_bairro = QLineEdit()
            self.entrada_bairro.setPlaceholderText("Digite o bairro (com autocomplete)")
            bairro_layout.addWidget(label_bairro)
            bairro_layout.addWidget(self.entrada_bairro)
            demo_layout.addLayout(bairro_layout)
            
            # Configurar autocomplete inicial
            self.atualizar_bairros()
            
            # Botão de teste
            self.botao_teste = QPushButton("🔍 Testar Seleção")
            self.botao_teste.clicked.connect(self.testar_selecao)
            demo_layout.addWidget(self.botao_teste)
            
            # Resultado
            self.label_resultado = QLabel("Selecione um estado e bairro, depois clique em 'Testar Seleção'")
            self.label_resultado.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.label_resultado.setWordWrap(True)
            demo_layout.addWidget(self.label_resultado)
            
            main_layout.addWidget(demo_frame)
            
            # Aplicar estilos
            self.aplicar_estilos()
        
        def atualizar_bairros(self):
            """Atualiza autocomplete de bairros baseado no estado selecionado"""
            estado_selecionado = self.combo_estado.currentText()
            bairros = obter_bairros_estado(estado_selecionado)
            
            completer = QCompleter(bairros)
            completer.setCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
            self.entrada_bairro.setCompleter(completer)
            
            print(f"Estado selecionado: {estado_selecionado}")
            print(f"Bairros disponíveis: {len(bairros)}")
        
        def testar_selecao(self):
            """Testa a seleção atual"""
            estado = self.combo_estado.currentText()
            bairro = self.entrada_bairro.text()
            
            if bairro.strip():
                resultado = f"🎯 LOCALIZAÇÃO SELECIONADA:\n\n📍 Estado: {estado}\n🏘️ Bairro: {bairro}\n\n🔍 Busca seria: '{bairro}, {estado}'"
                self.label_resultado.setStyleSheet("color: #27ae60; font-weight: bold; background-color: #d5f4e6; padding: 15px; border-radius: 8px;")
            else:
                resultado = "⚠️ Por favor, digite um bairro!"
                self.label_resultado.setStyleSheet("color: #e74c3c; font-weight: bold; background-color: #fadbd8; padding: 15px; border-radius: 8px;")
            
            self.label_resultado.setText(resultado)
            print(f"Estado: {estado}")
            print(f"Bairro: {bairro}")
        
        def aplicar_estilos(self):
            """Aplica estilos para garantir visibilidade"""
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #ecf0f1;
                }
                QWidget {
                    background-color: #ecf0f1;
                    font-family: 'Segoe UI', Arial, sans-serif;
                }
                #demoFrame {
                    background-color: white;
                    border-radius: 12px;
                    border: 2px solid #bdc3c7;
                }
                QComboBox {
                    padding: 12px;
                    border: 2px solid #3498db;
                    border-radius: 8px;
                    background-color: white;
                    color: #2c3e50;
                    font-size: 14px;
                    font-weight: bold;
                    min-height: 25px;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 25px;
                    background-color: #3498db;
                }
                QComboBox::down-arrow {
                    image: none;
                    border-left: 6px solid transparent;
                    border-right: 6px solid transparent;
                    border-top: 6px solid white;
                    margin-right: 8px;
                }
                QComboBox QAbstractItemView {
                    background-color: white;
                    color: #2c3e50;
                    border: 2px solid #3498db;
                    border-radius: 8px;
                    selection-background-color: #3498db;
                    selection-color: white;
                    padding: 5px;
                    font-weight: bold;
                }
                QComboBox QAbstractItemView::item {
                    padding: 10px;
                    border-bottom: 1px solid #ecf0f1;
                    color: #2c3e50;
                }
                QComboBox QAbstractItemView::item:hover {
                    background-color: #85c1e9;
                    color: white;
                }
                QComboBox QAbstractItemView::item:selected {
                    background-color: #3498db;
                    color: white;
                }
                QLineEdit {
                    padding: 12px;
                    border: 2px solid #27ae60;
                    border-radius: 8px;
                    background-color: white;
                    color: #2c3e50;
                    font-size: 14px;
                    min-height: 25px;
                }
                QLineEdit:focus {
                    border-color: #2ecc71;
                }
                QLabel {
                    color: #2c3e50;
                    font-weight: bold;
                    font-size: 14px;
                }
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    padding: 15px;
                    border: none;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 16px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
                QPushButton:pressed {
                    background-color: #a93226;
                }
            """)
    
    # Executar aplicação
    app = QApplication(sys.argv)
    
    # Criar e mostrar janela
    demo = DemoInterface()
    demo.show()
    
    print("✅ CORREÇÕES APLICADAS COM SUCESSO!")
    print("✅ Estados agora estão visíveis (texto escuro)")
    print("✅ Aplicação abre em tela cheia")
    print("\n🎯 Teste a interface:")
    print("1. Veja que os estados estão visíveis no dropdown")
    print("2. Selecione diferentes estados")
    print("3. Digite bairros e veja o autocomplete")
    print("4. Clique em 'Testar Seleção'")
    
    # Não executar o loop no modo de teste
    # sys.exit(app.exec())
    
except ImportError as e:
    print(f"❌ Erro de importação: {e}")
    print("Instale o PyQt6: pip install PyQt6")
except Exception as e:
    print(f"❌ Erro: {e}")
