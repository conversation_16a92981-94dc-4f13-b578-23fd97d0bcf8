#!/usr/bin/env python3
"""
Script para iniciar o TeemoFlow em modo de desenvolvimento
"""
import os
import sys
import subprocess
import time
import threading
from pathlib import Path

def run_command(command, cwd=None, name="Process"):
    """Executa um comando em uma thread separada"""
    def target():
        try:
            print(f"🚀 Iniciando {name}...")
            process = subprocess.Popen(
                command,
                shell=True,
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Ler output em tempo real
            for line in process.stdout:
                print(f"[{name}] {line.strip()}")
                
        except Exception as e:
            print(f"❌ Erro ao executar {name}: {e}")
    
    thread = threading.Thread(target=target, daemon=True)
    thread.start()
    return thread

def check_dependencies():
    """Verifica se as dependências estão instaladas"""
    print("🔍 Verificando dependências...")
    
    # Verificar Python
    python_version = sys.version_info
    if python_version.major < 3 or python_version.minor < 8:
        print("❌ Python 3.8+ é necessário")
        return False
    print(f"✅ Python {python_version.major}.{python_version.minor}")
    
    # Verificar Node.js
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js {result.stdout.strip()}")
        else:
            print("❌ Node.js não encontrado")
            return False
    except FileNotFoundError:
        print("❌ Node.js não encontrado")
        return False
    
    return True

def setup_backend():
    """Configura o backend"""
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Diretório backend não encontrado")
        return False
    
    print("🔧 Configurando backend...")
    
    # Verificar se requirements.txt existe
    requirements_file = backend_dir / "requirements.txt"
    if not requirements_file.exists():
        print("❌ requirements.txt não encontrado no backend")
        return False
    
    # Instalar dependências Python
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], cwd=backend_dir, check=True)
        print("✅ Dependências Python instaladas")
    except subprocess.CalledProcessError:
        print("❌ Erro ao instalar dependências Python")
        return False
    
    # Criar arquivo .env se não existir
    env_file = backend_dir / ".env"
    env_example = backend_dir / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        try:
            with open(env_example, 'r') as f:
                content = f.read()
            with open(env_file, 'w') as f:
                f.write(content)
            print("✅ Arquivo .env criado")
        except Exception as e:
            print(f"⚠️ Erro ao criar .env: {e}")
    
    return True

def setup_frontend():
    """Configura o frontend"""
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ Diretório frontend não encontrado")
        return False
    
    print("🔧 Configurando frontend...")
    
    # Verificar se package.json existe
    package_file = frontend_dir / "package.json"
    if not package_file.exists():
        print("❌ package.json não encontrado no frontend")
        return False
    
    # Instalar dependências Node.js
    try:
        subprocess.run(["npm", "install"], cwd=frontend_dir, check=True)
        print("✅ Dependências Node.js instaladas")
    except subprocess.CalledProcessError:
        print("❌ Erro ao instalar dependências Node.js")
        return False
    
    return True

def main():
    """Função principal"""
    print("🎯 TeemoFlow - Iniciando ambiente de desenvolvimento")
    print("=" * 50)
    
    # Verificar dependências
    if not check_dependencies():
        print("❌ Dependências não atendidas")
        return 1
    
    # Configurar backend
    if not setup_backend():
        print("❌ Erro na configuração do backend")
        return 1
    
    # Configurar frontend
    if not setup_frontend():
        print("❌ Erro na configuração do frontend")
        return 1
    
    print("\n🚀 Iniciando serviços...")
    print("=" * 50)
    
    # Iniciar backend
    backend_thread = run_command(
        "uvicorn main:app --reload --host 0.0.0.0 --port 8000",
        cwd="backend",
        name="Backend"
    )
    
    # Aguardar um pouco para o backend iniciar
    time.sleep(3)
    
    # Iniciar frontend
    frontend_thread = run_command(
        "npm run dev",
        cwd="frontend",
        name="Frontend"
    )
    
    print("\n✅ Serviços iniciados!")
    print("=" * 50)
    print("🌐 Frontend: http://localhost:3000")
    print("🔧 Backend API: http://localhost:8000")
    print("📚 Documentação API: http://localhost:8000/docs")
    print("\n💡 Pressione Ctrl+C para parar os serviços")
    
    try:
        # Manter o script rodando
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Parando serviços...")
        print("✅ TeemoFlow encerrado")
        return 0

if __name__ == "__main__":
    sys.exit(main())
